<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CashCompare - Find the Best Cashback Deals</title>
    <meta name="description" content="Compare cashback rates from top platforms. Find the best deals and maximize your savings with our comprehensive cashback comparison tool.">
    
    <script src="https://cdn.tailwindcss.com"></script>
    
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <script>
        // Custom Tailwind configuration (optional, for custom fonts and colors if needed)
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                        poppins: ['Poppins', 'sans-serif'],
                    },
                    colors: {
                        'primary-green': '#10B981',
                        'primary-dark': '#059669',
                        'primary-light': '#34D399',
                        'secondary-green': '#2ED573',
                        'accent-orange': '#F59E0B',
                        'purple-accent': '#8B5CF6',
                        'blue-accent': '#3B82F6',
                        'red-accent': '#EF4444',
                        'bg-primary-light-green': '#F0FDF4', // --bg-primary
                        'bg-secondary-light-green': '#ECFDF5', // --bg-secondary
                        'bg-accent-light-yellow': '#FEF3C7', // --bg-accent
                        'text-primary-dark-gray': '#1F2937', // --text-primary
                        'text-secondary-medium-gray': '#4B5563', // --text-secondary
                        'text-light-gray': '#6B7280', // --text-light
                    },
                    backgroundImage: {
                        'gradient-hero': 'linear-gradient(135deg, #10B981 0%, #34D399 50%, #6EE7B7 100%)',
                        'gradient-card': 'linear-gradient(145deg, #FFFFFF 0%, #F0FDF4 100%)',
                        'gradient-button': 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
                    },
                    boxShadow: {
                        'soft-green': '0 4px 20px rgba(16, 185, 129, 0.15)',
                        'medium-green': '0 8px 30px rgba(16, 185, 129, 0.2)',
                        'strong-green': '0 15px 40px rgba(16, 185, 129, 0.25)',
                        'card-gray': '0 10px 25px rgba(31, 41, 55, 0.08)',
                    },
                    keyframes: {
                        float: {
                          '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
                          '50%': { transform: 'translateY(-20px) rotate(2deg)' },
                        }
                    },
                    animation: {
                        float: 'float 6s ease-in-out infinite',
                    }
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .bg-clip-text {
                -webkit-background-clip: text;
                background-clip: text;
            }
            .text-fill-transparent {
                -webkit-text-fill-color: transparent;
                text-fill-color: transparent; /* Standard property */
            }
            /* Custom gradient border for search container - this is hard to do with pure Tailwind utilities */
            .search-container-gradient-border {
                position: relative;
                background-color: white; /* Fallback */
                background-clip: padding-box; /* Important for border to show */
                border: 3px solid transparent;
            }
            .search-container-gradient-border::before {
                content: '';
                position: absolute;
                top: 0; right: 0; bottom: 0; left: 0;
                z-index: -1;
                margin: -3px; /* Same as border width */
                border-radius: inherit; /* Follows parent's border-radius */
                background: linear-gradient(135deg, #10B981 0%, #34D399 50%, #6EE7B7 100%);
            }

            .hero-clip-path::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 60%;
                height: 100%;
                background: linear-gradient(135deg, #10B981 0%, #34D399 50%, #6EE7B7 100%);
                clip-path: polygon(30% 0%, 100% 0%, 100% 100%, 0% 100%);
                opacity: 0.1;
            }

            .btn-shine::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.5s ease;
            }
            .btn-shine:hover::before {
                left: 100%;
            }
        }
    </style>
</head>
<body class="font-inter leading-relaxed text-text-primary-dark-gray bg-bg-primary-light-green">
    <header class="bg-white/95 backdrop-blur-xl border-b border-pink-500/10 fixed top-0 left-0 right-0 z-[1000] transition-all duration-300 ease-in-out">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <nav class="flex justify-between items-center py-6">
                <a href="#" class="font-poppins text-3xl font-extrabold bg-gradient-button bg-clip-text text-fill-transparent no-underline flex items-center gap-2">
                    <i class="fas fa-bolt bg-gradient-button bg-clip-text text-fill-transparent"></i> CashBoost
                </a>
                
                <ul class="hidden lg:flex list-none gap-12">
                    <li><a href="#top-rates" class="no-underline text-text-secondary-medium-gray font-semibold text-base relative transition-all duration-300 ease-in-out hover:text-primary-green after:content-[''] after:absolute after:bottom-[-5px] after:left-0 after:w-0 after:h-[3px] after:bg-gradient-button after:rounded-sm after:transition-all after:duration-300 after:ease-in-out hover:after:w-full">Top Rates</a></li>
                    <li><a href="#offers" class="no-underline text-text-secondary-medium-gray font-semibold text-base relative transition-all duration-300 ease-in-out hover:text-primary-green after:content-[''] after:absolute after:bottom-[-5px] after:left-0 after:w-0 after:h-[3px] after:bg-gradient-button after:rounded-sm after:transition-all after:duration-300 after:ease-in-out hover:after:w-full">Live Deals</a></li>
                    <li><a href="#platforms" class="no-underline text-text-secondary-medium-gray font-semibold text-base relative transition-all duration-300 ease-in-out hover:text-primary-green after:content-[''] after:absolute after:bottom-[-5px] after:left-0 after:w-0 after:h-[3px] after:bg-gradient-button after:rounded-sm after:transition-all after:duration-300 after:ease-in-out hover:after:w-full">Compare Platforms</a></li>
                    <li><a href="#about" class="no-underline text-text-secondary-medium-gray font-semibold text-base relative transition-all duration-300 ease-in-out hover:text-primary-green after:content-[''] after:absolute after:bottom-[-5px] after:left-0 after:w-0 after:h-[3px] after:bg-gradient-button after:rounded-sm after:transition-all after:duration-300 after:ease-in-out hover:after:w-full">About</a></li>
                </ul>
                
                <div class="flex gap-4 items-center">
                    <a href="#" class="btn-shine relative overflow-hidden py-3 px-6 md:py-4 md:px-8 rounded-full no-underline font-semibold text-sm md:text-base transition-all duration-300 ease-in-out border-2 border-primary-green text-primary-green bg-transparent hover:bg-primary-green hover:text-white hover:-translate-y-0.5 hover:shadow-soft-green inline-flex items-center gap-3">Log In</a>
                    <a href="#" class="btn-shine relative overflow-hidden py-3 px-6 md:py-4 md:px-8 rounded-full no-underline font-semibold text-sm md:text-base transition-all duration-300 ease-in-out bg-gradient-button text-white shadow-soft-green hover:-translate-y-0.5 hover:shadow-medium-green inline-flex items-center gap-3">Sign Up Free</a>
                </div>
                <div class="lg:hidden">
                    <button id="mobile-menu-button" class="text-text-primary-dark-gray focus:outline-none">
                        <i class="fas fa-bars text-2xl"></i>
                    </button>
                </div>
            </nav>
        </div>
        <div id="mobile-menu" class="hidden lg:hidden">
            <ul class="flex flex-col items-center list-none gap-4 py-4">
                 <li><a href="#top-rates" class="no-underline text-text-secondary-medium-gray font-semibold text-base">Top Rates</a></li>
                 <li><a href="#offers" class="no-underline text-text-secondary-medium-gray font-semibold text-base">Live Deals</a></li>
                 <li><a href="#platforms" class="no-underline text-text-secondary-medium-gray font-semibold text-base">Compare Platforms</a></li>
                 <li><a href="#about" class="no-underline text-text-secondary-medium-gray font-semibold text-base">About</a></li>
            </ul>
        </div>
    </header>

    <section class="bg-bg-secondary-light-green pt-40 pb-24 relative overflow-hidden min-h-screen flex items-center hero-clip-path">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="hero-content relative z-[2] grid lg:grid-cols-2 gap-16 items-center">
                <div class="hero-text max-w-xl text-center lg:text-left">
                    <h1 class="font-poppins text-4xl sm:text-5xl lg:text-6xl font-black leading-tight mb-8 text-text-primary-dark-gray">Compare <span class="highlight relative bg-gradient-button bg-clip-text text-fill-transparent after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-full after:h-2 after:bg-accent-orange after:opacity-30 after:rounded-md">Cashback Rates</span> Instantly</h1>
                    <p class="text-lg sm:text-xl leading-relaxed text-text-secondary-medium-gray mb-12">Monitor and compare cashback percentages from all major platforms. Find the highest rates, track the best deals, and never miss out on savings again.</p>

                    <div class="search-container search-container-gradient-border bg-white rounded-[25px] p-1 shadow-card-gray mb-8">
                        <form class="search-form flex flex-col sm:flex-row gap-4 items-center">
                            <input type="text" class="flex-grow py-4 sm:py-5 px-6 border-none rounded-[20px] text-base sm:text-lg outline-none bg-bg-primary-light-green text-text-primary-dark-gray font-medium placeholder-text-light-gray w-full" placeholder="Search stores to compare cashback rates...">
                            <button type="submit" class="py-4 sm:py-5 px-6 sm:px-10 bg-gradient-button text-white border-none rounded-[20px] font-bold text-base sm:text-lg cursor-pointer transition-all duration-300 ease-in-out shadow-soft-green hover:-translate-y-0.5 hover:shadow-medium-green w-full sm:w-auto flex items-center justify-center gap-2">
                                <i class="fas fa-search"></i> Compare Rates
                            </button>
                        </form>
                    </div>

                    <div class="hero-features flex flex-wrap justify-center lg:justify-start gap-x-8 gap-y-4 mt-8">
                        <div class="feature-item flex items-center gap-3 text-text-secondary-medium-gray font-medium">
                            <i class="fas fa-chart-line text-primary-green text-lg"></i>
                            <span>Real-time Rate Tracking</span>
                        </div>
                        <div class="feature-item flex items-center gap-3 text-text-secondary-medium-gray font-medium">
                            <i class="fas fa-bell text-primary-green text-lg"></i>
                            <span>Deal Alerts</span>
                        </div>
                        <div class="feature-item flex items-center gap-3 text-text-secondary-medium-gray font-medium">
                            <i class="fas fa-shield-alt text-primary-green text-lg"></i>
                            <span>100% Free</span>
                        </div>
                    </div>
                </div>

                <div class="hero-visual relative flex items-center justify-center order-first lg:order-last">
                    <div class="hero-illustration w-full max-w-md lg:max-w-lg h-80 lg:h-96 bg-gradient-card rounded-3xl shadow-card-gray flex items-center justify-center relative overflow-hidden">
                        <div class="floating-cards absolute w-full h-full">
                            <div class="floating-card store-card absolute bg-white rounded-2xl p-4 shadow-card-gray border-2 border-bg-secondary-light-green animate-float top-[20%] left-[10%] min-w-[120px] text-center" style="animation-delay: 0s;">
                                <div class="store-logo amazon w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3 text-2xl text-white bg-[#FF9900]">
                                    <i class="fab fa-amazon"></i>
                                </div>
                                <div class="cashback-rate text-xl font-extrabold text-primary-green mb-1">12.5%</div>
                                <div class="store-name font-semibold text-text-primary-dark-gray text-sm mb-0.5">Amazon</div>
                                <div class="platform-info text-xs text-text-light-gray">via Rakuten</div>
                            </div>
                            <div class="floating-card store-card absolute bg-white rounded-2xl p-4 shadow-card-gray border-2 border-bg-secondary-light-green animate-float top-[60%] right-[15%] min-w-[120px] text-center" style="animation-delay: 2s;">
                                <div class="store-logo target w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3 text-2xl text-white bg-[#CC0000]">
                                    <i class="fas fa-bullseye"></i>
                                </div>
                                <div class="cashback-rate text-xl font-extrabold text-primary-green mb-1">8.0%</div>
                                <div class="store-name font-semibold text-text-primary-dark-gray text-sm mb-0.5">Target</div>
                                <div class="platform-info text-xs text-text-light-gray">via TopCashback</div>
                            </div>
                            <div class="floating-card store-card absolute bg-white rounded-2xl p-4 shadow-card-gray border-2 border-bg-secondary-light-green animate-float bottom-[20%] left-[20%] min-w-[120px] text-center" style="animation-delay: 4s;">
                                <div class="store-logo walmart w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3 text-2xl text-white bg-[#004C91]">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="cashback-rate text-xl font-extrabold text-primary-green mb-1">5.5%</div>
                                <div class="store-name font-semibold text-text-primary-dark-gray text-sm mb-0.5">Walmart</div>
                                <div class="platform-info text-xs text-text-light-gray">via Ibotta</div>
                            </div>
                        </div>
                        <div class="text-6xl text-primary-green opacity-30">
                            <i class="fas fa-coins"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="stats grid md:grid-cols-3 gap-12 mt-16">
                <div class="stat text-center bg-white p-8 rounded-2xl shadow-card-gray transition-transform duration-300 ease-in-out hover:-translate-y-1">
                    <span class="stat-number font-poppins text-5xl font-extrabold block bg-gradient-button bg-clip-text text-fill-transparent">$2.4B+</span>
                    <div class="stat-label text-text-secondary-medium-gray font-semibold mt-2 text-lg">Total Cashback Earned</div>
                </div>
                <div class="stat text-center bg-white p-8 rounded-2xl shadow-card-gray transition-transform duration-300 ease-in-out hover:-translate-y-1">
                    <span class="stat-number font-poppins text-5xl font-extrabold block bg-gradient-button bg-clip-text text-fill-transparent">5M+</span>
                    <div class="stat-label text-text-secondary-medium-gray font-semibold mt-2 text-lg">Active Users</div>
                </div>
                <div class="stat text-center bg-white p-8 rounded-2xl shadow-card-gray transition-transform duration-300 ease-in-out hover:-translate-y-1">
                    <span class="stat-number font-poppins text-5xl font-extrabold block bg-gradient-button bg-clip-text text-fill-transparent">10,000+</span>
                    <div class="stat-label text-text-secondary-medium-gray font-semibold mt-2 text-lg">Partner Stores</div>
                </div>
            </div>
        </div>
    </section>

    <section id="top-rates" class="py-20 sm:py-32 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="section-title text-center font-poppins text-4xl sm:text-5xl font-extrabold mb-6 text-text-primary-dark-gray relative after:content-[''] after:absolute after:bottom-[-10px] after:left-1/2 after:-translate-x-1/2 after:w-24 after:h-1.5 after:bg-gradient-button after:rounded-md">Top Cashback Rates Today</h2>
            <p class="section-subtitle text-center text-lg sm:text-xl text-text-light-gray mb-16 max-w-3xl mx-auto leading-relaxed">Live comparison of the highest cashback percentages across all major platforms</p>

            <div class="rates-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-16">
                <div class="rate-card featured bg-gradient-card rounded-2xl p-8 shadow-card-gray transition-all duration-300 ease-in-out relative border-2 border-primary-green hover:-translate-y-1 hover:shadow-strong-green">
                    <div class="rate-badge absolute -top-3 right-5 bg-gradient-button text-white py-2 px-4 rounded-full text-sm font-semibold shadow-soft-green">Highest Rate</div>
                    <div class="store-header flex items-center gap-4 mb-6">
                        <div class="store-logo-large amazon w-16 h-16 rounded-2xl flex items-center justify-center text-3xl text-white shadow-soft-green bg-[#FF9900]">
                            <i class="fab fa-amazon"></i>
                        </div>
                        <div class="store-info">
                            <h3 class="font-poppins text-xl font-bold text-text-primary-dark-gray mb-1">Amazon</h3>
                            <p class="text-text-light-gray text-sm">Electronics, Books, Home</p>
                        </div>
                    </div>
                    <div class="rate-comparison mb-6">
                        <div class="best-rate text-center mb-4 p-4 bg-bg-secondary-light-green rounded-2xl border-2 border-primary-green">
                            <span class="rate-value block font-poppins text-4xl font-extrabold text-primary-green leading-none">12.5%</span>
                            <span class="platform text-sm text-text-secondary-medium-gray font-semibold">Rakuten</span>
                        </div>
                        <div class="other-rates flex flex-col gap-2">
                            <div class="rate-item flex justify-between items-center py-2 px-4 bg-gray-50 rounded-lg">
                                <span class="platform text-text-secondary-medium-gray text-sm">TopCashback</span>
                                <span class="rate font-bold text-text-primary-dark-gray text-sm">10.0%</span>
                            </div>
                            <div class="rate-item flex justify-between items-center py-2 px-4 bg-gray-50 rounded-lg">
                                <span class="platform text-text-secondary-medium-gray text-sm">Honey</span>
                                <span class="rate font-bold text-text-primary-dark-gray text-sm">8.5%</span>
                            </div>
                        </div>
                    </div>
                    <a href="#" class="btn-shine relative overflow-hidden w-full justify-center py-4 px-4 bg-gradient-button text-white rounded-full font-bold transition-all duration-300 ease-in-out hover:-translate-y-0.5 hover:shadow-medium-green inline-flex items-center gap-2">Get 12.5% Cashback</a>
                </div>

                <div class="rate-card bg-gradient-card rounded-2xl p-8 shadow-card-gray transition-all duration-300 ease-in-out relative border-2 border-transparent hover:-translate-y-1 hover:shadow-strong-green hover:border-primary-green">
                    <div class="store-header flex items-center gap-4 mb-6">
                        <div class="store-logo-large target w-16 h-16 rounded-2xl flex items-center justify-center text-3xl text-white shadow-soft-green bg-[#CC0000]">
                            <i class="fas fa-bullseye"></i>
                        </div>
                        <div class="store-info">
                            <h3 class="font-poppins text-xl font-bold text-text-primary-dark-gray mb-1">Target</h3>
                            <p class="text-text-light-gray text-sm">Fashion, Home, Beauty</p>
                        </div>
                    </div>
                    <div class="rate-comparison mb-6">
                        <div class="best-rate text-center mb-4 p-4 bg-bg-secondary-light-green rounded-2xl border-2 border-primary-green">
                            <span class="rate-value block font-poppins text-4xl font-extrabold text-primary-green leading-none">8.0%</span>
                            <span class="platform text-sm text-text-secondary-medium-gray font-semibold">TopCashback</span>
                        </div>
                        <div class="other-rates flex flex-col gap-2">
                            <div class="rate-item flex justify-between items-center py-2 px-4 bg-gray-50 rounded-lg">
                                <span class="platform text-text-secondary-medium-gray text-sm">Rakuten</span>
                                <span class="rate font-bold text-text-primary-dark-gray text-sm">7.5%</span>
                            </div>
                            <div class="rate-item flex justify-between items-center py-2 px-4 bg-gray-50 rounded-lg">
                                <span class="platform text-text-secondary-medium-gray text-sm">Ibotta</span>
                                <span class="rate font-bold text-text-primary-dark-gray text-sm">6.0%</span>
                            </div>
                        </div>
                    </div>
                     <a href="#" class="btn-shine relative overflow-hidden w-full justify-center py-4 px-4 border-2 border-primary-green text-primary-green bg-transparent rounded-full font-bold transition-all duration-300 ease-in-out hover:bg-primary-green hover:text-white hover:-translate-y-0.5 hover:shadow-soft-green inline-flex items-center gap-2">Get 8.0% Cashback</a>
                </div>

                <div class="rate-card bg-gradient-card rounded-2xl p-8 shadow-card-gray transition-all duration-300 ease-in-out relative border-2 border-transparent hover:-translate-y-1 hover:shadow-strong-green hover:border-primary-green">
                    <div class="store-header flex items-center gap-4 mb-6">
                        <div class="store-logo-large walmart w-16 h-16 rounded-2xl flex items-center justify-center text-3xl text-white shadow-soft-green bg-[#004C91]">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="store-info">
                            <h3 class="font-poppins text-xl font-bold text-text-primary-dark-gray mb-1">Walmart</h3>
                            <p class="text-text-light-gray text-sm">Groceries, Electronics</p>
                        </div>
                    </div>
                    <div class="rate-comparison mb-6">
                        <div class="best-rate text-center mb-4 p-4 bg-bg-secondary-light-green rounded-2xl border-2 border-primary-green">
                            <span class="rate-value block font-poppins text-4xl font-extrabold text-primary-green leading-none">5.5%</span>
                            <span class="platform text-sm text-text-secondary-medium-gray font-semibold">Ibotta</span>
                        </div>
                        <div class="other-rates flex flex-col gap-2">
                            <div class="rate-item flex justify-between items-center py-2 px-4 bg-gray-50 rounded-lg">
                                <span class="platform text-text-secondary-medium-gray text-sm">Rakuten</span>
                                <span class="rate font-bold text-text-primary-dark-gray text-sm">4.0%</span>
                            </div>
                            <div class="rate-item flex justify-between items-center py-2 px-4 bg-gray-50 rounded-lg">
                                <span class="platform text-text-secondary-medium-gray text-sm">TopCashback</span>
                                <span class="rate font-bold text-text-primary-dark-gray text-sm">3.5%</span>
                            </div>
                        </div>
                    </div>
                    <a href="#" class="btn-shine relative overflow-hidden w-full justify-center py-4 px-4 border-2 border-primary-green text-primary-green bg-transparent rounded-full font-bold transition-all duration-300 ease-in-out hover:bg-primary-green hover:text-white hover:-translate-y-0.5 hover:shadow-soft-green inline-flex items-center gap-2">Get 5.5% Cashback</a>
                </div>
            </div>

            <div class="text-center mt-12">
                <a href="#" class="btn-shine relative overflow-hidden py-4 px-8 bg-gradient-button text-white rounded-full font-bold transition-all duration-300 ease-in-out hover:-translate-y-0.5 hover:shadow-medium-green inline-flex items-center gap-3 text-lg">
                    <i class="fas fa-chart-bar"></i> View All Store Comparisons
                </a>
            </div>
        </div>
    </section>
    
    <section id="how-it-works" class="py-20 sm:py-32 bg-bg-primary-light-green">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="section-title text-center font-poppins text-4xl sm:text-5xl font-extrabold mb-6 text-text-primary-dark-gray relative after:content-[''] after:absolute after:bottom-[-10px] after:left-1/2 after:-translate-x-1/2 after:w-24 after:h-1.5 after:bg-gradient-button after:rounded-md">How CashBoost Works</h2>
            <p class="section-subtitle text-center text-lg sm:text-xl text-text-light-gray mb-16 max-w-3xl mx-auto leading-relaxed">Earning cashback is simple. Follow these easy steps to start saving on your online purchases.</p>
            <div class="steps grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12 mt-16">
                <div class="step bg-gradient-card p-8 lg:p-12 rounded-3xl text-center shadow-card-gray transition-all duration-300 ease-in-out relative overflow-hidden hover:-translate-y-2 hover:shadow-strong-green before:content-[''] before:absolute before:top-0 before:left-0 before:w-full before:h-1.5 before:bg-gradient-hero">
                    <div class="step-icon w-20 h-20 bg-gradient-button rounded-3xl flex items-center justify-center mx-auto mb-8 text-white text-3xl shadow-soft-green">
                        <i class="fas fa-search-dollar"></i>
                    </div>
                    <h3 class="font-poppins text-2xl font-bold mb-4 text-text-primary-dark-gray">1. Find Your Store</h3>
                    <p class="text-text-light-gray text-base lg:text-lg leading-relaxed">Search for your favorite store or browse categories to find the best cashback offers.</p>
                </div>
                <div class="step bg-gradient-card p-8 lg:p-12 rounded-3xl text-center shadow-card-gray transition-all duration-300 ease-in-out relative overflow-hidden hover:-translate-y-2 hover:shadow-strong-green before:content-[''] before:absolute before:top-0 before:left-0 before:w-full before:h-1.5 before:bg-gradient-hero">
                    <div class="step-icon w-20 h-20 bg-gradient-button rounded-3xl flex items-center justify-center mx-auto mb-8 text-white text-3xl shadow-soft-green">
                        <i class="fas fa-mouse-pointer"></i>
                    </div>
                    <h3 class="font-poppins text-2xl font-bold mb-4 text-text-primary-dark-gray">2. Shop as Usual</h3>
                    <p class="text-text-light-gray text-base lg:text-lg leading-relaxed">Click through to the store's website from CashBoost and complete your purchase like you normally would.</p>
                </div>
                <div class="step bg-gradient-card p-8 lg:p-12 rounded-3xl text-center shadow-card-gray transition-all duration-300 ease-in-out relative overflow-hidden hover:-translate-y-2 hover:shadow-strong-green before:content-[''] before:absolute before:top-0 before:left-0 before:w-full before:h-1.5 before:bg-gradient-hero">
                    <div class="step-icon w-20 h-20 bg-gradient-button rounded-3xl flex items-center justify-center mx-auto mb-8 text-white text-3xl shadow-soft-green">
                        <i class="fas fa-hand-holding-usd"></i>
                    </div>
                    <h3 class="font-poppins text-2xl font-bold mb-4 text-text-primary-dark-gray">3. Earn Cashback</h3>
                    <p class="text-text-light-gray text-base lg:text-lg leading-relaxed">After your purchase is confirmed, cashback will be added to your CashBoost account. Withdraw your earnings easily!</p>
                </div>
            </div>
        </div>
    </section>

    <section id="offers" class="py-16 sm:py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="section-title text-center font-poppins text-4xl sm:text-5xl font-extrabold mb-6 text-text-primary-dark-gray relative after:content-[''] after:absolute after:bottom-[-10px] after:left-1/2 after:-translate-x-1/2 after:w-24 after:h-1.5 after:bg-gradient-button after:rounded-md">Today's Best Cashback Offers</h2>
            <p class="section-subtitle text-center text-lg sm:text-xl text-text-light-gray mb-12 max-w-3xl mx-auto leading-relaxed">Don't miss out on these limited-time high cashback rates from top retailers.</p>

            <div class="offers-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-12">
                <div class="offer-card bg-white rounded-xl p-6 shadow-lg border border-gray-200 transition-all duration-300 ease-in-out relative overflow-hidden hover:-translate-y-1 hover:shadow-xl">
                    <div class="offer-badge absolute top-4 right-4 bg-secondary-green text-white py-1 px-3 rounded-full text-xs font-semibold">Hot Deal</div>
                    <div class="offer-logo w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mb-4 font-bold text-primary-green">AMZ</div>
                    <h3 class="offer-title text-lg font-semibold mb-2">Amazon</h3>
                    <div class="offer-rate text-2xl font-bold text-secondary-green mb-2">Up to 15%</div>
                    <p class="offer-description text-text-secondary-medium-gray text-sm mb-4">Electronics, Books, Home & Garden</p>
                    <a href="#" class="btn-shine relative overflow-hidden w-full bg-gradient-button text-white py-3 rounded-lg font-semibold inline-flex items-center justify-center gap-2">Get Cashback</a>
                </div>
                 <div class="offer-card bg-white rounded-xl p-6 shadow-lg border border-gray-200 transition-all duration-300 ease-in-out relative overflow-hidden hover:-translate-y-1 hover:shadow-xl">
                    <div class="offer-badge absolute top-4 right-4 bg-amber-500 text-white py-1 px-3 rounded-full text-xs font-semibold">Limited Time</div>
                    <div class="offer-logo w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mb-4 font-bold text-primary-green">WMT</div>
                    <h3 class="offer-title text-lg font-semibold mb-2">Walmart</h3>
                    <div class="offer-rate text-2xl font-bold text-secondary-green mb-2">Up to 8%</div>
                    <p class="offer-description text-text-secondary-medium-gray text-sm mb-4">Groceries, Clothing, Electronics</p>
                    <a href="#" class="btn-shine relative overflow-hidden w-full bg-gradient-button text-white py-3 rounded-lg font-semibold inline-flex items-center justify-center gap-2">Get Cashback</a>
                </div>
                <div class="offer-card bg-white rounded-xl p-6 shadow-lg border border-gray-200 transition-all duration-300 ease-in-out relative overflow-hidden hover:-translate-y-1 hover:shadow-xl">
                    <div class="offer-logo w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mb-4 font-bold text-primary-green">TGT</div>
                    <h3 class="offer-title text-lg font-semibold mb-2">Target</h3>
                    <div class="offer-rate text-2xl font-bold text-secondary-green mb-2">Up to 12%</div>
                    <p class="offer-description text-text-secondary-medium-gray text-sm mb-4">Fashion, Home, Beauty, Kids</p>
                    <a href="#" class="btn-shine relative overflow-hidden w-full bg-gradient-button text-white py-3 rounded-lg font-semibold inline-flex items-center justify-center gap-2">Get Cashback</a>
                </div>
                 <div class="offer-card bg-white rounded-xl p-6 shadow-lg border border-gray-200 transition-all duration-300 ease-in-out relative overflow-hidden hover:-translate-y-1 hover:shadow-xl">
                    <div class="offer-badge absolute top-4 right-4 bg-blue-500 text-white py-1 px-3 rounded-full text-xs font-semibold">New</div>
                    <div class="offer-logo w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mb-4 font-bold text-primary-green">BBY</div>
                    <h3 class="offer-title text-lg font-semibold mb-2">Best Buy</h3>
                    <div class="offer-rate text-2xl font-bold text-secondary-green mb-2">Up to 6%</div>
                    <p class="offer-description text-text-secondary-medium-gray text-sm mb-4">Electronics, Gaming, Appliances</p>
                    <a href="#" class="btn-shine relative overflow-hidden w-full bg-gradient-button text-white py-3 rounded-lg font-semibold inline-flex items-center justify-center gap-2">Get Cashback</a>
                </div>
                <div class="offer-card bg-white rounded-xl p-6 shadow-lg border border-gray-200 transition-all duration-300 ease-in-out relative overflow-hidden hover:-translate-y-1 hover:shadow-xl">
                    <div class="offer-logo w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mb-4 font-bold text-primary-green">MCY</div>
                    <h3 class="offer-title text-lg font-semibold mb-2">Macy's</h3>
                    <div class="offer-rate text-2xl font-bold text-secondary-green mb-2">Up to 10%</div>
                    <p class="offer-description text-text-secondary-medium-gray text-sm mb-4">Fashion, Beauty, Home Decor</p>
                    <a href="#" class="btn-shine relative overflow-hidden w-full bg-gradient-button text-white py-3 rounded-lg font-semibold inline-flex items-center justify-center gap-2">Get Cashback</a>
                </div>
                 <div class="offer-card bg-white rounded-xl p-6 shadow-lg border border-gray-200 transition-all duration-300 ease-in-out relative overflow-hidden hover:-translate-y-1 hover:shadow-xl">
                    <div class="offer-badge absolute top-4 right-4 bg-purple-accent text-white py-1 px-3 rounded-full text-xs font-semibold">Popular</div>
                    <div class="offer-logo w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mb-4 font-bold text-primary-green">NKE</div>
                    <h3 class="offer-title text-lg font-semibold mb-2">Nike</h3>
                    <div class="offer-rate text-2xl font-bold text-secondary-green mb-2">Up to 7%</div>
                    <p class="offer-description text-text-secondary-medium-gray text-sm mb-4">Athletic Wear, Shoes, Accessories</p>
                    <a href="#" class="btn-shine relative overflow-hidden w-full bg-gradient-button text-white py-3 rounded-lg font-semibold inline-flex items-center justify-center gap-2">Get Cashback</a>
                </div>
            </div>

            <div class="text-center mt-8">
                <a href="#" class="btn-shine relative overflow-hidden py-3 px-6 border-2 border-primary-green text-primary-green bg-transparent rounded-full font-semibold transition-all duration-300 ease-in-out hover:bg-primary-green hover:text-white hover:-translate-y-0.5 hover:shadow-soft-green inline-flex items-center">View All 10,000+ Stores</a>
            </div>
        </div>
    </section>

    <section id="platforms" class="py-16 sm:py-20 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="section-title text-center font-poppins text-4xl sm:text-5xl font-extrabold mb-6 text-text-primary-dark-gray relative after:content-[''] after:absolute after:bottom-[-10px] after:left-1/2 after:-translate-x-1/2 after:w-24 after:h-1.5 after:bg-gradient-button after:rounded-md">Compare Top Cashback Platforms</h2>
            <p class="section-subtitle text-center text-lg sm:text-xl text-text-light-gray mb-12 max-w-3xl mx-auto leading-relaxed">See how different cashback platforms stack up against each other.</p>

            <div class="comparison-table bg-white rounded-xl overflow-hidden shadow-xl mt-12">
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse">
                        <thead>
                            <tr class="bg-primary-green text-white">
                                <th class="p-4 sm:p-6 text-left font-semibold">Platform</th>
                                <th class="p-4 sm:p-6 text-center font-semibold">Avg. Cashback</th>
                                <th class="p-4 sm:p-6 text-center font-semibold">Store Count</th>
                                <th class="p-4 sm:p-6 text-center font-semibold">Payout Methods</th>
                                <th class="p-4 sm:p-6 text-center font-semibold">Min. Payout</th>
                                <th class="p-4 sm:p-6 text-center font-semibold">Rating</th>
                            </tr>
                        </thead>
                        <tbody class="text-text-secondary-medium-gray">
                            <tr class="border-b border-gray-200">
                                <td class="p-4 sm:p-6">
                                    <div class="flex items-center gap-3 sm:gap-4">
                                        <div class="w-10 h-10 bg-[#ff6b35] rounded-lg flex items-center justify-center text-white font-bold">R</div>
                                        <span class="font-semibold text-text-primary-dark-gray">Rakuten</span>
                                    </div>
                                </td>
                                <td class="p-4 sm:p-6 text-center font-semibold text-secondary-green">2.5%</td>
                                <td class="p-4 sm:p-6 text-center">3,500+</td>
                                <td class="p-4 sm:p-6 text-center">PayPal, Check</td>
                                <td class="p-4 sm:p-6 text-center">$5.01</td>
                                <td class="p-4 sm:p-6 text-center text-amber-400">★★★★☆</td>
                            </tr>
                            <tr class="border-b border-gray-200 bg-gray-50">
                                <td class="p-4 sm:p-6">
                                    <div class="flex items-center gap-3 sm:gap-4">
                                        <div class="w-10 h-10 bg-primary-green rounded-lg flex items-center justify-center text-white font-bold">T</div>
                                        <span class="font-semibold text-text-primary-dark-gray">TopCashback</span>
                                    </div>
                                </td>
                                <td class="p-4 sm:p-6 text-center font-semibold text-secondary-green">3.2%</td>
                                <td class="p-4 sm:p-6 text-center">7,000+</td>
                                <td class="p-4 sm:p-6 text-center">Bank, PayPal, Gift Cards</td>
                                <td class="p-4 sm:p-6 text-center">$10.00</td>
                                <td class="p-4 sm:p-6 text-center text-amber-400">★★★★★</td>
                            </tr>
                            <tr class="border-b border-gray-200">
                                <td class="p-4 sm:p-6">
                                    <div class="flex items-center gap-3 sm:gap-4">
                                        <div class="w-10 h-10 bg-accent-orange rounded-lg flex items-center justify-center text-white font-bold">H</div>
                                        <span class="font-semibold text-text-primary-dark-gray">Honey</span>
                                    </div>
                                </td>
                                <td class="p-4 sm:p-6 text-center font-semibold text-secondary-green">1.8%</td>
                                <td class="p-4 sm:p-6 text-center">30,000+</td>
                                <td class="p-4 sm:p-6 text-center">PayPal</td>
                                <td class="p-4 sm:p-6 text-center">$5.00</td>
                                <td class="p-4 sm:p-6 text-center text-amber-400">★★★☆☆</td>
                            </tr>
                            <tr class="bg-gray-50">
                                <td class="p-4 sm:p-6">
                                    <div class="flex items-center gap-3 sm:gap-4">
                                        <div class="w-10 h-10 bg-purple-accent rounded-lg flex items-center justify-center text-white font-bold">I</div>
                                        <span class="font-semibold text-text-primary-dark-gray">Ibotta</span>
                                    </div>
                                </td>
                                <td class="p-4 sm:p-6 text-center font-semibold text-secondary-green">2.1%</td>
                                <td class="p-4 sm:p-6 text-center">1,500+</td>
                                <td class="p-4 sm:p-6 text-center">PayPal, Venmo, Gift Cards</td>
                                <td class="p-4 sm:p-6 text-center">$20.00</td>
                                <td class="p-4 sm:p-6 text-center text-amber-400">★★★★☆</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="text-center mt-8">
                <a href="#" class="btn-shine relative overflow-hidden py-3 px-6 bg-gradient-button text-white rounded-full font-semibold transition-all duration-300 ease-in-out hover:-translate-y-0.5 hover:shadow-medium-green inline-flex items-center">Compare All Platforms</a>
            </div>
        </div>
    </section>

    <section class="trust-section py-16 sm:py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="section-title text-center font-poppins text-4xl sm:text-5xl font-extrabold mb-6 text-text-primary-dark-gray relative after:content-[''] after:absolute after:bottom-[-10px] after:left-1/2 after:-translate-x-1/2 after:w-24 after:h-1.5 after:bg-gradient-button after:rounded-md">Trusted by Millions</h2>
            <p class="section-subtitle text-center text-lg sm:text-xl text-text-light-gray mb-12 max-w-3xl mx-auto leading-relaxed">Join the community of smart shoppers who save money every day.</p>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 mt-12">
                <div class="text-center p-6 sm:p-8 bg-white rounded-xl shadow-lg">
                    <div class="text-4xl sm:text-5xl text-secondary-green mb-4 sm:mb-6">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="text-xl sm:text-2xl font-semibold mb-2 sm:mb-3 text-text-primary-dark-gray">Secure & Safe</h3>
                    <p class="text-text-secondary-medium-gray text-sm sm:text-base">Bank-level security with SSL encryption. Your data is always protected.</p>
                </div>
                <div class="text-center p-6 sm:p-8 bg-white rounded-xl shadow-lg">
                    <div class="text-4xl sm:text-5xl text-secondary-green mb-4 sm:mb-6">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="text-xl sm:text-2xl font-semibold mb-2 sm:mb-3 text-text-primary-dark-gray">Fast Payouts</h3>
                    <p class="text-text-secondary-medium-gray text-sm sm:text-base">Get your cashback quickly with multiple payout options available.</p>
                </div>
                <div class="text-center p-6 sm:p-8 bg-white rounded-xl shadow-lg">
                    <div class="text-4xl sm:text-5xl text-secondary-green mb-4 sm:mb-6">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3 class="text-xl sm:text-2xl font-semibold mb-2 sm:mb-3 text-text-primary-dark-gray">24/7 Support</h3>
                    <p class="text-text-secondary-medium-gray text-sm sm:text-base">Our dedicated support team is here to help you maximize your savings.</p>
                </div>
                <div class="text-center p-6 sm:p-8 bg-white rounded-xl shadow-lg">
                    <div class="text-4xl sm:text-5xl text-secondary-green mb-4 sm:mb-6">
                        <i class="fas fa-gift"></i>
                    </div>
                    <h3 class="text-xl sm:text-2xl font-semibold mb-2 sm:mb-3 text-text-primary-dark-gray">Free to Use</h3>
                    <p class="text-text-secondary-medium-gray text-sm sm:text-base">No fees, no subscriptions. Start earning cashback immediately.</p>
                </div>
            </div>
        </div>
    </section>

    <section class="final-cta py-16 sm:py-20 bg-gradient-to-r from-primary-green to-primary-light text-white text-center">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl sm:text-4xl font-bold mb-4">Ready to Start Earning Cashback?</h2>
            <p class="text-lg sm:text-xl mb-8 opacity-90">Join millions of smart shoppers and start maximizing your savings today.</p>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="#" class="btn-shine relative overflow-hidden bg-white text-primary-green py-3 px-8 sm:py-4 sm:px-10 rounded-full text-base sm:text-lg font-semibold transition-all duration-300 ease-in-out hover:bg-gray-100 inline-flex items-center justify-center gap-2">
                    <i class="fas fa-user-plus"></i> Sign Up Free
                </a>
                <a href="#" class="btn-shine relative overflow-hidden bg-transparent text-white border-2 border-white py-3 px-8 sm:py-4 sm:px-10 rounded-full text-base sm:text-lg font-semibold transition-all duration-300 ease-in-out hover:bg-white/20 inline-flex items-center justify-center gap-2">
                    <i class="fas fa-play"></i> Watch Demo
                </a>
            </div>

            <div class="mt-8 text-sm opacity-80">
                <i class="fas fa-check"></i> No credit card required &bull; <i class="fas fa-check"></i> Free forever &bull; <i class="fas fa-check"></i> Cancel anytime
            </div>
        </div>
    </section>

    <footer class="bg-text-primary-dark-gray text-white py-12 sm:py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
                <div>
                    <div class="flex items-center gap-2 mb-4">
                        <i class="fas fa-bolt text-primary-green text-2xl"></i>
                        <span class="text-2xl font-bold">CashBoost</span>
                    </div>
                    <p class="text-gray-400 mb-4 text-sm">The ultimate cashback comparison platform. Find the best deals and maximize your savings.</p>
                    <div class="flex gap-4">
                        <a href="#" class="text-gray-400 hover:text-primary-green text-xl transition-colors duration-300"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-gray-400 hover:text-primary-green text-xl transition-colors duration-300"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-400 hover:text-primary-green text-xl transition-colors duration-300"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-gray-400 hover:text-primary-green text-xl transition-colors duration-300"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>

                <div>
                    <h4 class="font-semibold mb-4 text-lg">Company</h4>
                    <ul class="list-none text-gray-400 space-y-2 text-sm">
                        <li><a href="#" class="hover:text-primary-green transition-colors duration-300">About Us</a></li>
                        <li><a href="#" class="hover:text-primary-green transition-colors duration-300">How It Works</a></li>
                        <li><a href="#" class="hover:text-primary-green transition-colors duration-300">Careers</a></li>
                        <li><a href="#" class="hover:text-primary-green transition-colors duration-300">Press</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="font-semibold mb-4 text-lg">Support</h4>
                    <ul class="list-none text-gray-400 space-y-2 text-sm">
                        <li><a href="#" class="hover:text-primary-green transition-colors duration-300">Help Center</a></li>
                        <li><a href="#" class="hover:text-primary-green transition-colors duration-300">Contact Us</a></li>
                        <li><a href="#" class="hover:text-primary-green transition-colors duration-300">FAQ</a></li>
                        <li><a href="#" class="hover:text-primary-green transition-colors duration-300">Status</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="font-semibold mb-4 text-lg">Legal</h4>
                    <ul class="list-none text-gray-400 space-y-2 text-sm">
                        <li><a href="#" class="hover:text-primary-green transition-colors duration-300">Privacy Policy</a></li>
                        <li><a href="#" class="hover:text-primary-green transition-colors duration-300">Terms of Service</a></li>
                        <li><a href="#" class="hover:text-primary-green transition-colors duration-300">Cookie Policy</a></li>
                        <li><a href="#" class="hover:text-primary-green transition-colors duration-300">Disclaimer</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-700 pt-8 text-center text-gray-500 text-sm">
                <p>&copy; 2024 CashBoost. All rights reserved. | Made with ❤️ for smart shoppers</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    // Adjust for fixed header height if necessary
                    // const headerOffset = document.querySelector('header').offsetHeight;
                    // const elementPosition = targetElement.getBoundingClientRect().top;
                    // const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
                    // window.scrollTo({
                    //      top: offsetPosition,
                    //      behavior: "smooth"
                    // });
                     targetElement.scrollIntoView({
                         behavior: 'smooth',
                         block: 'start' // Can be 'start', 'center', 'end', or 'nearest'
                     });
                }
            });
        });

        // Search functionality
        document.querySelector('.search-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const searchTerm = document.querySelector('.search-input').value;
            if (searchTerm.trim()) {
                // Replace alert with a more modern notification if possible
                // For now, keeping the alert as per original code
                // In a real app, you'd redirect or fetch results here.
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-[1001] p-4';
                modal.innerHTML = `
                    <div class="bg-white p-6 rounded-lg shadow-xl max-w-sm w-full text-center">
                        <h3 class="text-xl font-semibold mb-4 text-gray-800">Search Submitted</h3>
                        <p class="text-gray-600 mb-4">Searching for cashback rates for: "${searchTerm}"</p>
                        <p class="text-sm text-gray-500 mb-6">This would normally redirect to a search results page.</p>
                        <button id="close-search-modal" class="bg-primary-green text-white px-4 py-2 rounded-lg hover:bg-primary-dark">Close</button>
                    </div>
                `;
                document.body.appendChild(modal);
                document.getElementById('close-search-modal').addEventListener('click', () => {
                    modal.remove();
                });
            }
        });

        // Animate stats on scroll
        function animateStats() {
            const stats = document.querySelectorAll('.stat-number');
            if (!stats.length) return;

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !entry.target.dataset.animated) {
                        entry.target.dataset.animated = true; // Mark as animated
                        const target = entry.target;
                        const finalValueText = target.textContent; // e.g., "$2.4B+" or "5M+"
                        
                        // Extract numeric part and suffix
                        const numericMatch = finalValueText.match(/([\d.]+)/);
                        const numericValue = numericMatch ? parseFloat(numericMatch[1]) : 0;
                        const suffix = finalValueText.replace(/[\d.]/g, ''); // e.g., "B+", "M+"

                        let current = 0;
                        const duration = 1500; // ms
                        const frameDuration = 1000 / 60; // 60fps
                        const totalFrames = Math.round(duration / frameDuration);
                        const increment = numericValue / totalFrames;
                        
                        let frame = 0;
                        const timer = setInterval(() => {
                            frame++;
                            current += increment;
                            if (frame >= totalFrames) {
                                current = numericValue; // Ensure it ends on the exact value
                                clearInterval(timer);
                            }
                            // Format with one decimal place if it's not an integer, then add suffix
                            target.textContent = (numericValue % 1 !== 0 ? current.toFixed(1) : Math.round(current)) + suffix;
                        }, frameDuration);
                        observer.unobserve(target);
                    }
                });
            }, { threshold: 0.5 }); // Trigger when 50% visible

            stats.forEach(stat => observer.observe(stat));
        }
        
        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');

        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });
        }


        // Add loading states to buttons (excluding search submit)
        document.querySelectorAll('.btn-shine, .final-cta a, .offers-grid a, .rates-grid a, .platform-comparison a').forEach(button => {
            // Check if it's the search button to avoid double handling
            if (button.closest('.search-form')) return;

            button.addEventListener('click', function(e) {
                // Only apply to non-navigational buttons or if href is #
                if (this.tagName === 'A' && (this.getAttribute('href') === '#' || this.getAttribute('href').startsWith('#') && this.getAttribute('href').length > 1)) {
                    // This is a scroll link, handled by smooth scroll. Don't show loading.
                } else if (this.type !== 'submit') { // Avoid conflict with form submit
                    e.preventDefault(); // Prevent default if it's an anchor acting as a button
                    const originalContent = this.innerHTML;
                    const icon = this.querySelector('i');
                    let originalIconClass = '';
                    if (icon) {
                        originalIconClass = icon.className; // Save original icon
                        icon.className = 'fas fa-spinner fa-spin'; // Change to spinner
                        this.childNodes.forEach(node => { // Remove text nodes
                            if (node.nodeType === Node.TEXT_NODE) {
                                node.nodeValue = ' Loading...';
                            }
                        });
                    } else {
                         this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
                    }
                    this.classList.add('opacity-75', 'pointer-events-none');


                    setTimeout(() => {
                        this.innerHTML = originalContent; // Restore original content
                        this.classList.remove('opacity-75', 'pointer-events-none');
                    }, 1500);
                }
            });
        });
        
        // Initialize animations when page loads
        document.addEventListener('DOMContentLoaded', function() {
            animateStats();
        });

    </script>
</body>
</html>
